import { notNullish } from '../common'

/**
 * Type alias for error codes to improve type safety and reusability
 * Supports string, number, and symbol for maximum flexibility
 */
export type ErrorCode = string | number | symbol

export interface BaseErrorOptions extends ErrorOptions {
    name?: string
    code?: ErrorCode
    retryable?: boolean
}

/**
 * Utility function to format a single cause in the error chain
 * @param cause - The error cause to format
 * @param visited - WeakSet to track visited objects for circular reference detection
 * @param isInCauseChain - Flag to prevent recursive toString() calls
 * @returns Formatted cause string
 */
function formatCause(cause: unknown, visited: WeakSet<object>, isInCauseChain = false): string {
    if (cause == null) {
        return String(cause)
    }

    // Handle circular reference detection for objects
    if (typeof cause === 'object' && visited.has(cause)) {
        return '[Circular reference detected]'
    }

    if (typeof cause === 'object') {
        visited.add(cause)
    }

    if (cause instanceof Error) {
        // For BaseError, avoid calling toString() recursively when building cause chain
        // Instead, format manually to prevent infinite recursion
        if (cause instanceof BaseError && isInCauseChain) {
            const parts: string[] = []

            if (notNullish(cause.code)) {
                parts.push(`[${String(cause.code)}]`)
            }

            parts.push(`${cause.name}: ${cause.message}`)

            return parts.join(' ')
        }

        // For regular errors or when not in cause chain, format normally
        return `${cause.name}: ${cause.message}`
    }

    return String(cause)
}

/**
 * Utility function to build the complete cause chain string
 * @param cause - The root cause to start from
 * @param maxDepth - Maximum depth to traverse (default: 3)
 * @returns Formatted cause chain string
 */
function buildCauseChain(cause: unknown, maxDepth = 3): string {
    if (!cause || maxDepth <= 0) {
        return ''
    }

    const visited = new WeakSet<object>()
    const parts: string[] = []
    let currentCause = cause
    let depth = 0

    while (currentCause && depth < maxDepth) {
        const formatted = formatCause(currentCause, visited, true)
        parts.push(formatted)

        // Move to next cause if it's an Error
        if (currentCause instanceof Error && currentCause.cause) {
            currentCause = currentCause.cause
            depth++
        } else {
            break
        }
    }

    if (currentCause && depth >= maxDepth) {
        parts.push('[Additional causes truncated...]')
    }

    return parts.join('\n  Caused by: ')
}

export abstract class BaseError extends Error {
    public readonly timestamp: Date
    public readonly code?: ErrorCode
    public readonly retryable?: boolean

    // Cache for toString() to avoid repeated computation
    private _toStringCache?: string
    private _lastToStringTime?: number

    public constructor(message?: string, { name, code, retryable, ...options }: BaseErrorOptions = {}) {
        super(message, options)

        this.name = name ?? this.constructor.name
        this.timestamp = new Date()
        this.code = code
        this.retryable = retryable

        Object.setPrototypeOf(this, new.target.prototype)

        if (notNullish(Error.captureStackTrace)) {
            Error.captureStackTrace(this, this.constructor)
        }
    }

    protected withValue<T>(key: string, value?: T): this {
        if (value !== undefined) {
            Object.defineProperty(this, key, { value, writable: false, enumerable: true, configurable: false })
        }

        return this
    }

    public toJSON() {
        const result: Record<string, unknown> = {}

        for (const key of Object.keys(this)) {
            result[key] = this[key as keyof this]
        }

        result.code = this.code
        result.timestamp = this.timestamp.toISOString()
        result.name = this.name
        result.message = this.message
        result.stack = this.stack

        if (this.cause) {
            result.cause = this.cause instanceof Error ? (this.cause as Error & { toJSON?(): unknown }).toJSON?.() ?? this.cause : this.cause
        }

        return result
    }

    /**
     * Enhanced toString method with cause chain support and performance optimization
     * @returns Formatted error string including cause chain
     */
    public override toString(): string {
        const now = Date.now()

        // Use cache if available and recent (within 1 second) to handle repeated calls efficiently
        if (this._toStringCache && this._lastToStringTime && (now - this._lastToStringTime) < 1000) {
            return this._toStringCache
        }

        const parts: string[] = []

        // Add error code if present
        if (notNullish(this.code)) {
            parts.push(`[${String(this.code)}]`)
        }

        // Add name and message (core error info)
        parts.push(`${this.name}: ${this.message}`)

        // Build the main error string
        let result = parts.join(' ')

        // Add cause chain if present
        if (this.cause) {
            const causeChain = buildCauseChain(this.cause)

            if (causeChain) {
                result += `\n  Caused by: ${causeChain}`
            }
        }

        // Cache the result for performance
        this._toStringCache = result
        this._lastToStringTime = now

        return result
    }
}
